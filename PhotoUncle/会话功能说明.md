# PhotoUncle 会话功能说明

## 功能概述

PhotoUncle 现在支持会话管理功能，可以记录和快速访问您最近打开的图片目录。

## 主要功能

### 1. 自动会话创建
- 当您选择一个目录时，应用会自动创建一个会话
- 会话名称默认为目录名称
- 会话会记录目录路径、创建时间和最后访问时间

### 2. 会话菜单
- 在菜单栏中找到"会话"菜单
- 显示最近访问的10个会话
- 每个会话显示名称和完整路径
- 前9个会话支持快捷键 Cmd+1 到 Cmd+9

### 3. 会话持久化
- 会话信息自动保存到 UserDefaults
- 应用重启后会话列表保持不变
- 最多保留20个最近会话

### 4. 智能会话管理
- 重复打开相同目录会更新现有会话的访问时间
- 自动过滤无效的目录（已删除或移动的目录）
- 支持清除所有会话的功能

## 使用方法

### 创建会话
1. 点击"选择目录"按钮
2. 选择包含图片的文件夹
3. 应用自动创建会话并开始加载图片

### 访问会话
1. 点击菜单栏中的"会话"菜单
2. 从"最近会话"列表中选择要打开的会话
3. 或使用快捷键 Cmd+1 到 Cmd+9 快速访问前9个会话

### 管理会话
- 在"会话"菜单的"管理"部分可以清除所有会话
- 无效的会话（目录不存在）会自动从列表中移除

## 技术实现

### 核心组件
- `Session.swift`: 会话数据模型
- `SessionManager.swift`: 会话管理器（单例模式）
- `PhotoUncleApp.swift`: 菜单集成
- `ContentView.swift`: 会话功能集成

### 数据存储
- 使用 UserDefaults 进行持久化存储
- JSON 编码/解码会话数据
- 键名：`PhotoUncle_Sessions` 和 `PhotoUncle_CurrentSession`

### 会话验证
- 启动时自动验证会话有效性
- 过滤掉不存在的目录
- 支持目录存在性检查

## 调试功能

在调试模式下，应用启动时会运行会话管理器测试，输出测试结果到控制台。

## 未来改进

- 支持会话重命名
- 添加会话收藏功能
- 支持会话分组
- 添加会话搜索功能
- 支持导入/导出会话配置
