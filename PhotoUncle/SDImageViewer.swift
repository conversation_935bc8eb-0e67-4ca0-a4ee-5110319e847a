//
//  SDImageViewer.swift
//  PhotoUncle
//
//  Created by wtb on 2025/7/12.
//

import SwiftUI
import SDWebImageSwiftUI

struct SDImageViewer: View {
    let imageURL: URL

    @State private var scale: CGFloat = 1.0
    @State private var rotation: Angle = .degrees(0)
    @State private var offset: CGSize = .zero
    @State private var dragOffset: CGSize = .zero
    @State private var imageSize: CGSize = .zero
    @State private var showControls: Bool = true
    @State private var isLoading: Bool = true
    @State private var lastScale: CGFloat = 1.0
    @State private var hideControlsTimer: Timer?
    @State private var isDragging: Bool = false

    // 优化缩放范围
    private let minScale: CGFloat = 0.1
    private let maxScale: CGFloat = 50.0

    var body: some View {
        GeometryReader { geometry in
            let containerSize = geometry.size
            let fitScale = fitScaleFor(imageSize: imageSize, containerSize: containerSize)

            ZStack {
                // 渐变背景
                LinearGradient(
                    colors: [Color.black, Color.gray.opacity(0.3), Color.black],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )

                if isLoading {
                    // 加载指示器
                    VStack(spacing: 16) {
                        ProgressView()
                            .scaleEffect(1.5)
                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        Text("加载中...")
                            .foregroundColor(.white.opacity(0.8))
                            .font(.system(size: 14, weight: .medium))
                    }
                }

                WebImage(url: imageURL)
                    .onSuccess { image, data, cacheType in
                        // 获取图片尺寸
                        print("🖼️ 图片加载成功: \(image.size)")
                        imageSize = image.size
                        isLoading = false
                    }
                    .onFailure { error in
                        print("❌ 图片加载失败: \(error)")
                        isLoading = false
                    }
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .scaleEffect(fitScale * scale)
                    .rotationEffect(rotation)
                    .offset(x: offset.width + dragOffset.width,
                            y: offset.height + dragOffset.height)
                    .overlay(
                        // 拖动时的视觉反馈
                        Group {
                            if isDragging {
                                RoundedRectangle(cornerRadius: 8)
                                    .stroke(Color.white.opacity(0.3), lineWidth: 2)
                                    .scaleEffect(1.02)
                                    .animation(.easeInOut(duration: 0.1), value: isDragging)
                            }
                        }
                    )
                    .background(
                        // 使用背景视图来处理手势，避免冲突
                        GestureHandler(
                            onDragChanged: { translation in
                                print("🎯 拖拽中: \(translation)")
                                isDragging = true
                                dragOffset = translation
                            },
                            onDragEnded: { translation in
                                print("🎯 拖拽结束: \(translation)")
                                isDragging = false
                                offset.width += translation.width
                                offset.height += translation.height
                                dragOffset = .zero
                            },
                            onScrollWheel: { deltaY in
                                print("🎯 滚轮事件: deltaY = \(deltaY)")
                                let zoomFactor: CGFloat = 1.1
                                withAnimation(.easeInOut(duration: 0.1)) {
                                    if deltaY > 0 {
                                        scale = min(maxScale, scale * zoomFactor)
                                    } else {
                                        scale = max(minScale, scale / zoomFactor)
                                    }
                                }
                            }
                        )
                    )
                    .onAppear {
                        print("🔄 SDImageViewer appeared")
                    }
                    .onDisappear {
                        print("🔄 SDImageViewer disappeared")
                    }
                    .simultaneousGesture(
                        // 缩放手势 - 使用simultaneousGesture与拖拽共存
                        MagnificationGesture()
                            .onChanged { value in
                                print("🔍 缩放手势: value = \(value), lastScale = \(lastScale)")
                                let delta = value / lastScale
                                scale = min(maxScale, max(minScale, scale * delta))
                                lastScale = value
                                print("🔍 新scale = \(scale)")
                            }
                            .onEnded { _ in
                                print("🔍 缩放手势结束")
                                lastScale = 1.0
                            }
                    )
                    .onTapGesture(count: 2) {
                        // 双击重置
                        print("👆 双击手势")
                        if !isDragging {
                            print("👆 执行双击重置")
                            withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                                scale = 1.0
                                rotation = .zero
                                offset = .zero
                            }
                        }
                    }
                    .onTapGesture(count: 1) {
                        // 单击显示/隐藏控制栏
                        print("👆 单击手势")
                        if !isDragging {
                            print("👆 切换控制栏显示")
                            withAnimation(.easeInOut(duration: 0.3)) {
                                showControls.toggle()
                            }
                        }
                    }
                    .onHover { hovering in
                        print("🖱️ hover: \(hovering)")
                        // 取消之前的隐藏定时器
                        hideControlsTimer?.invalidate()
                        
                        if hovering {
                            // 鼠标进入时立即显示控制栏
                            print("🖱️ 显示控制栏")
                            withAnimation(.easeInOut(duration: 0.3)) {
                                showControls = true
                            }
                        } else {
                            // 鼠标离开时延迟隐藏控制栏，避免在控制面板上操作时闪烁
                            print("🖱️ 延迟隐藏控制栏")
                            hideControlsTimer = Timer.scheduledTimer(withTimeInterval: 0.5, repeats: false) { _ in
                                withAnimation(.easeInOut(duration: 0.3)) {
                                    showControls = false
                                }
                            }
                        }
                    }

                // 控制面板 - 只在显示时可见
                if showControls {
                    VStack {
                        HStack {
                            Spacer()
                            controlPanel
                                .padding(16)
                        }
                        Spacer()
                        // 底部缩放控制
                        HStack {
                            zoomControlPanel
                                .padding(.horizontal, 20)
                                .padding(.bottom, 20)
                        }
                    }
                    .onHover { hovering in
                        // 当鼠标悬停在控制面板上时，取消隐藏定时器并保持显示状态
                        hideControlsTimer?.invalidate()
                        if hovering {
                            showControls = true
                        }
                    }
                }
            }
        }
        .clipped()
        .background(Color.black)
        .onDisappear {
            // 清理定时器
            hideControlsTimer?.invalidate()
            hideControlsTimer = nil
        }
    }

    var controlPanel: some View {
        HStack(spacing: 16) {
            // 旋转控制
            ControlButton(
                icon: "rotate.left",
                action: {
                    withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                        rotation += .degrees(-90)
                    }
                }
            )
            
            ControlButton(
                icon: "rotate.right",
                action: {
                    withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                        rotation += .degrees(90)
                    }
                }
            )

            Divider()
                .frame(height: 24)
                .background(Color.white.opacity(0.3))

            // 缩放控制
            ControlButton(
                icon: "minus.magnifyingglass",
                action: {
                    withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
                        scale = max(minScale, scale / 1.5)
                    }
                }
            )
            
            ControlButton(
                icon: "plus.magnifyingglass",
                action: {
                    withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
                        scale = min(maxScale, scale * 1.5)
                    }
                }
            )

            Divider()
                .frame(height: 24)
                .background(Color.white.opacity(0.3))

            // 重置按钮
            ControlButton(
                icon: "arrow.counterclockwise",
                action: {
                    withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                        scale = 1.0
                        rotation = .zero
                        offset = .zero
                    }
                }
            )
        }
        .padding(12)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
                .shadow(color: .black.opacity(0.3), radius: 10, x: 0, y: 5)
        )
    }

    var zoomControlPanel: some View {
        VStack(spacing: 12) {
            // 缩放百分比显示
            HStack {
                Text("缩放")
                    .foregroundColor(.white)
                    .font(.system(size: 13, weight: .medium))
                Spacer()
                Text("\(Int(scale * 100))%")
                    .foregroundColor(.white)
                    .font(.system(size: 13, weight: .semibold))
                    .monospacedDigit()
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(
                        RoundedRectangle(cornerRadius: 6)
                            .fill(.ultraThinMaterial)
                    )
            }

            // 缩放滑块
            HStack(spacing: 12) {
                Image(systemName: "minus.magnifyingglass")
                    .foregroundColor(.white)
                    .font(.system(size: 12, weight: .medium))

                Slider(value: $scale, in: minScale...maxScale) {
                    Text("缩放")
                } minimumValueLabel: {
                    Text("\(Int(minScale * 100))%")
                        .foregroundColor(.white.opacity(0.7))
                        .font(.system(size: 10, weight: .medium))
                } maximumValueLabel: {
                    Text("\(Int(maxScale * 100))%")
                        .foregroundColor(.white.opacity(0.7))
                        .font(.system(size: 10, weight: .medium))
                }
                .accentColor(.white)

                Image(systemName: "plus.magnifyingglass")
                    .foregroundColor(.white)
                    .font(.system(size: 12, weight: .medium))
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
                .shadow(color: .black.opacity(0.3), radius: 10, x: 0, y: 5)
        )
        .frame(maxWidth: 320)
    }

    /// 优化初始图片显示尺寸计算
    func fitScaleFor(imageSize: CGSize, containerSize: CGSize) -> CGFloat {
        guard imageSize.width > 0 && imageSize.height > 0 &&
              containerSize.width > 0 && containerSize.height > 0 else { return 1.0 }

        // 计算适配缩放比例
        let scaleToFitWidth = containerSize.width / imageSize.width
        let scaleToFitHeight = containerSize.height / imageSize.height

        // 选择较小的缩放比例以确保图片完全显示在容器内
        let fitScale = min(scaleToFitWidth, scaleToFitHeight)

        // 优化长图的显示
        let aspectRatio = imageSize.width / imageSize.height
        
        if aspectRatio < 0.3 {
            // 非常长的竖图，确保最小宽度为容器的50%
            let minWidth = containerSize.width * 0.5
            let widthScale = minWidth / imageSize.width
            return max(fitScale, widthScale)
        } else if aspectRatio < 0.7 {
            // 较长的竖图，确保最小宽度为容器的40%
            let minWidth = containerSize.width * 0.4
            let widthScale = minWidth / imageSize.width
            return max(fitScale, widthScale)
        } else if aspectRatio > 3.0 {
            // 非常宽的横图，确保最小高度为容器的50%
            let minHeight = containerSize.height * 0.5
            let heightScale = minHeight / imageSize.height
            return max(fitScale, heightScale)
        } else if aspectRatio > 1.5 {
            // 较宽的横图，确保最小高度为容器的40%
            let minHeight = containerSize.height * 0.4
            let heightScale = minHeight / imageSize.height
            return max(fitScale, heightScale)
        }

        // 对于正常比例的图片，允许适当放大
        if fitScale > 1.0 {
            return min(fitScale, 2.0) // 最大放大2倍
        }

        return fitScale
    }
}

// 手势处理器组件
struct GestureHandler: NSViewRepresentable {
    let onDragChanged: (CGSize) -> Void
    let onDragEnded: (CGSize) -> Void
    let onScrollWheel: (CGFloat) -> Void
    
    func makeNSView(context: Context) -> NSView {
        print("🔧 创建GestureHandler NSView")
        let view = GestureView()
        view.onDragChanged = onDragChanged
        view.onDragEnded = onDragEnded
        view.onScrollWheel = onScrollWheel
        return view
    }
    
    func updateNSView(_ nsView: NSView, context: Context) {
        print("🔧 更新GestureHandler NSView (1)")
    }
    
    class GestureView: NSView {
        var onDragChanged: ((CGSize) -> Void)?
        var onDragEnded: ((CGSize) -> Void)?
        var onScrollWheel: ((CGFloat) -> Void)?
        private var initialDragLocation: NSPoint?
        private var totalTranslation: CGSize = .zero
        
        override func mouseDown(with event: NSEvent) {
            print("🖱️ mouseDown: \(event.locationInWindow)")
            initialDragLocation = event.locationInWindow
            totalTranslation = .zero
            window?.makeFirstResponder(self)
        }
        
        override func mouseDragged(with event: NSEvent) {
            print("🖱️ mouseDragged: \(event.locationInWindow)")
            guard let initialLocation = initialDragLocation else { 
                print("❌ 没有初始位置")
                return 
            }
            let currentLocation = event.locationInWindow
            totalTranslation = CGSize(
                width: currentLocation.x - initialLocation.x,
                height: currentLocation.y - initialLocation.y
            )
            print("📏 计算偏移: \(totalTranslation)")
            onDragChanged?(totalTranslation)
        }
        
        override func mouseUp(with event: NSEvent) {
            print("🖱️ mouseUp: \(event.locationInWindow)")
            guard let initialLocation = initialDragLocation else { 
                print("❌ 没有初始位置")
                return 
            }
            let currentLocation = event.locationInWindow
            let finalTranslation = CGSize(
                width: currentLocation.x - initialLocation.x,
                height: currentLocation.y - initialLocation.y
            )
            print("📏 最终偏移: \(finalTranslation)")
            onDragEnded?(finalTranslation)
            initialDragLocation = nil
            totalTranslation = .zero
        }
        
        override func scrollWheel(with event: NSEvent) {
            print("🖱️ scrollWheel: deltaY = \(event.scrollingDeltaY)")
            onScrollWheel?(event.scrollingDeltaY)
        }
        
        override var acceptsFirstResponder: Bool {
            print("🔧 acceptsFirstResponder: true")
            return true
        }
        
        override func becomeFirstResponder() -> Bool {
            print("🔧 becomeFirstResponder: true")
            return true
        }
    }
}

// 控制按钮组件
struct ControlButton: View {
    let icon: String
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            Image(systemName: icon)
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.white)
                .frame(width: 36, height: 36)
                .background(
                    Circle()
                        .fill(.ultraThinMaterial)
                        .shadow(color: .black.opacity(0.2), radius: 4, x: 0, y: 2)
                )
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(1.0)
        .animation(.spring(response: 0.2, dampingFraction: 0.8), value: true)
    }
}

// 扩展View以支持滚轮事件（保留以备后用）
extension View {
    func onScrollWheel(perform action: @escaping (NSEvent) -> Void) -> some View {
        self.background(ScrollWheelHandler(onScrollWheel: action))
    }
}

struct ScrollWheelHandler: NSViewRepresentable {
    let onScrollWheel: (NSEvent) -> Void

    func makeNSView(context: Context) -> NSView {
        let view = ScrollWheelView()
        view.onScrollWheel = onScrollWheel
        return view
    }

    func updateNSView(_ nsView: NSView, context: Context) {
        print("🔧 更新ScrollWheelHandler NSView (2)")
    }

    class ScrollWheelView: NSView {
        var onScrollWheel: ((NSEvent) -> Void)?

        override func scrollWheel(with event: NSEvent) {
            onScrollWheel?(event)
        }

        override var acceptsFirstResponder: Bool {
            return true
        }
        
        override func becomeFirstResponder() -> Bool {
            return true
        }
    }
}

#Preview {
    if let url = URL(string: "https://picsum.photos/800/600") {
        SDImageViewer(imageURL: url)
            .frame(width: 800, height: 600)
    }
}
