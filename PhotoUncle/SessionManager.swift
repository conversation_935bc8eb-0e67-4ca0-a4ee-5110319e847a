//
//  SessionManager.swift
//  PhotoUncle
//
//  Created by wtb on 2025/7/12.
//

import Foundation
import SwiftUI

// MARK: - SessionManager
class SessionManager: ObservableObject {
    static let shared = SessionManager()
    
    @Published var sessions: [Session] = []
    @Published var currentSession: Session?
    
    private let userDefaults = UserDefaults.standard
    private let sessionsKey = "PhotoUncle_Sessions"
    private let currentSessionKey = "PhotoUncle_CurrentSession"
    
    private init() {
        loadSessions()
        loadCurrentSession()

    }
    
    // MARK: - Session Management
    
    /// 创建新会话
    func createSession(name: String, directoryPath: String) -> Session {
        let session = Session(name: name, directoryPath: directoryPath)
        
        // 检查是否已存在相同路径的会话
        if let existingIndex = sessions.firstIndex(where: { $0.directoryPath == directoryPath }) {
            // 更新现有会话的访问时间
            sessions[existingIndex] = sessions[existingIndex].withUpdatedAccessTime()
            currentSession = sessions[existingIndex]
            saveSessions()
            saveCurrentSession()
            return sessions[existingIndex]
        }
        
        // 添加新会话到列表开头
        sessions.insert(session, at: 0)
        currentSession = session
        
        // 限制会话数量（保留最近20个）
        if sessions.count > 20 {
            sessions = Array(sessions.prefix(20))
        }
        
        saveSessions()
        saveCurrentSession()
        
        return session
    }
    
    /// 打开现有会话
    func openSession(_ session: Session) {
        guard session.isDirectoryValid else {
            // 目录不存在，从列表中移除
            removeSession(session)
            return
        }
        
        // 更新访问时间并移到列表开头
        let updatedSession = session.withUpdatedAccessTime()
        
        if let index = sessions.firstIndex(where: { $0.id == session.id }) {
            sessions.remove(at: index)
        }
        
        sessions.insert(updatedSession, at: 0)
        currentSession = updatedSession
        
        saveSessions()
        saveCurrentSession()
    }
    
    /// 删除会话
    func removeSession(_ session: Session) {
        sessions.removeAll { $0.id == session.id }
        
        if currentSession?.id == session.id {
            currentSession = nil
        }
        
        saveSessions()
        saveCurrentSession()
    }
    
    /// 清除所有会话
    func clearAllSessions() {
        sessions.removeAll()
        currentSession = nil
        saveSessions()
        saveCurrentSession()
    }
    
    /// 获取最近的会话（按访问时间排序）
    func getRecentSessions(limit: Int = 10) -> [Session] {
        return Array(sessions.prefix(limit))
    }
    
    // MARK: - Persistence
    
    private func saveSessions() {
        do {
            let data = try JSONEncoder().encode(sessions)
            userDefaults.set(data, forKey: sessionsKey)
        } catch {
            print("保存会话失败: \(error)")
        }
    }
    
    private func loadSessions() {
        guard let data = userDefaults.data(forKey: sessionsKey) else { return }
        
        do {
            sessions = try JSONDecoder().decode([Session].self, from: data)
            // 过滤掉无效的会话（目录不存在）
            sessions = sessions.filter { $0.isDirectoryValid }
        } catch {
            print("加载会话失败: \(error)")
            sessions = []
        }
    }
    
    private func saveCurrentSession() {
        if let currentSession = currentSession {
            do {
                let data = try JSONEncoder().encode(currentSession)
                userDefaults.set(data, forKey: currentSessionKey)
            } catch {
                print("保存当前会话失败: \(error)")
            }
        } else {
            userDefaults.removeObject(forKey: currentSessionKey)
        }
    }
    
    private func loadCurrentSession() {
        guard let data = userDefaults.data(forKey: currentSessionKey) else { return }
        
        do {
            let session = try JSONDecoder().decode(Session.self, from: data)
            if session.isDirectoryValid {
                currentSession = session
            }
        } catch {
            print("加载当前会话失败: \(error)")
        }
    }
}
