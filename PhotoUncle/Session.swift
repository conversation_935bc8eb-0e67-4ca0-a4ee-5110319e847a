//
//  Session.swift
//  PhotoUncle
//
//  Created by wtb on 2025/7/12.
//

import Foundation

// MARK: - Session Model
struct Session: Identifiable, Codable, Hashable {
    let id: UUID
    let name: String
    let directoryPath: String
    let createdAt: Date
    let lastAccessedAt: Date
    
    init(name: String, directoryPath: String) {
        self.id = UUID()
        self.name = name
        self.directoryPath = directoryPath
        self.createdAt = Date()
        self.lastAccessedAt = Date()
    }
    
    init(id: UUID, name: String, directoryPath: String, createdAt: Date, lastAccessedAt: Date) {
        self.id = id
        self.name = name
        self.directoryPath = directoryPath
        self.createdAt = createdAt
        self.lastAccessedAt = lastAccessedAt
    }
    
    // 更新最后访问时间
    func withUpdatedAccessTime() -> Session {
        return Session(
            id: self.id,
            name: self.name,
            directoryPath: self.directoryPath,
            createdAt: self.createdAt,
            lastAccessedAt: Date()
        )
    }
    
    // 获取目录URL
    var directoryURL: URL {
        return URL(fileURLWithPath: directoryPath)
    }
    
    // 检查目录是否仍然存在
    var isDirectoryValid: Bool {
        var isDirectory: ObjCBool = false
        let exists = FileManager.default.fileExists(atPath: directoryPath, isDirectory: &isDirectory)
        return exists && isDirectory.boolValue
    }
    
    // 格式化的创建时间
    var formattedCreatedAt: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        return formatter.string(from: createdAt)
    }
    
    // 格式化的最后访问时间
    var formattedLastAccessedAt: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        return formatter.string(from: lastAccessedAt)
    }
}
